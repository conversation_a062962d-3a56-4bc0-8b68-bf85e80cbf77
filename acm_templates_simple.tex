% ACM 竞赛算法模板集 - 简化版本
% 编译命令: xelatex acm_templates_simple.tex

\documentclass[titlepage, a4paper]{article}
\usepackage{graphicx, amsmath, amssymb}
\usepackage{listings, xcolor}
\usepackage{setspace, titlesec, fancyhdr, multicol}
\usepackage[inner=1.25cm, outer=0.8cm, top=1.7cm, bottom=0.0cm]{geometry}
\usepackage{tocloft}
\usepackage{fontspec}
\usepackage[colorlinks, linkcolor = black]{hyperref}

% 配置字体
\usepackage{xeCJK}

\XeTeXlinebreaklocale "zh"
\XeTeXlinebreakskip = 0pt plus 1pt

\setlength{\parindent}{0em}\setlength{\parskip}{1pt}

% 配置页眉页脚
\pagestyle{fancy}
\setlength{\headsep}{0.1cm}
\chead{\leftmark}
\rhead{Page \thepage}
\lhead{ACM 算法模板}

% 配置双栏间距
\setlength{\columnsep}{13pt}

% 配置目录样式
\renewcommand\cftsecfont{\Large\bfseries}
\renewcommand\cftsubsecfont{\normalsize}
\setlength{\cftbeforesecskip}{3pt}
\setlength{\cftbeforesubsecskip}{1pt}

% 配置章节样式
\titleformat{\section}
{\huge\bfseries}
{\thesection.}
{4pt}
{}
\titleformat{\subsection}
{\large\bfseries}
{\thesubsection}
{4pt}
{}
\titlespacing{\section}{0pt}{2pt}{2pt}
\titlespacing{\subsection}{0pt}{1pt}{1pt}

% 配置代码高亮
\definecolor{codegreen}{rgb}{0,0.6,0}
\definecolor{codegray}{rgb}{0.5,0.5,0.5}
\definecolor{codepurple}{rgb}{0.58,0,0.82}
\definecolor{backcolour}{rgb}{0.95,0.95,0.92}

\lstdefinestyle{mystyle}{
    backgroundcolor=\color{backcolour},   
    commentstyle=\color{codegreen},
    keywordstyle=\color{magenta},
    numberstyle=\tiny\color{codegray},
    stringstyle=\color{codepurple},
    basicstyle=\ttfamily\footnotesize,
    breakatwhitespace=false,         
    breaklines=true,                 
    captionpos=b,                    
    keepspaces=true,                 
    numbers=left,                    
    numbersep=5pt,                  
    showspaces=false,                
    showstringspaces=false,
    showtabs=false,                  
    tabsize=2,
    frame=single,
    rulecolor=\color{black}
}

\lstset{style=mystyle}

% 自定义命令导入代码
\newcommand{\importcode}[2]{
    \subsection{#1}
    \lstinputlisting[language=C++]{#2}
}

\begin{document}
	\begin{titlepage}
		\centering
		\vspace*{2cm}
		
		{\Huge\bfseries ACM 竞赛算法模板集}
		
		\vspace{1.5cm}
		
		{\Large 数据结构 · 图论 · 数学 · 字符串 · 杂项}
		
		\vspace{2cm}
		
		\begin{center}
		\begin{tabular}{c}
		\textbf{包含内容：} \\[0.5cm]
		数据结构模板 \\
		图论算法模板 \\
		数学算法模板 \\
		字符串算法模板 \\
		实用工具模板 \\
		\end{tabular}
		\end{center}
		
		\vfill
		
		{\large \today}
	\end{titlepage}
	
	\begin{multicols}{2}
		\setcounter{tocdepth}{3}
		\begingroup
		\let\cleardoublepage\relax
		\let\clearpage\relax
		\begin{small}
		\begin{spacing}{0.70}
		\tableofcontents
		\end{spacing}
		\end{small}
		\newpage
		\begin{spacing}{0.6}

\section{数据结构 (Data Structures)}

\importcode{线段树 (Segment Tree)}{DS/SegmentTree.hpp}

\importcode{树状数组 (Binary Indexed Tree)}{DS/BinaryIndexedTree.hpp}

\section{数学 (Mathematics)}

\importcode{快速幂 (Fast Power)}{Math/FastPower.hpp}

\section{基础工具}

\importcode{类型定义 (Type Definitions)}{types.hpp}

		\end{spacing}
		\endgroup
	\end{multicols}
\end{document}
