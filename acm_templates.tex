\documentclass[titlepage, a4paper]{article}
\usepackage{graphicx, amssymb, amsmath, textcomp, booktabs}
\usepackage[libertine,vvarbb]{newtxmath}
\usepackage[scr=rsfso]{mathalfa}
\usepackage[T1]{fontenc}
\usepackage{minted}
\usepackage{listings, color, setspace, titlesec, fancyhdr, mdframed, multicol}
\usepackage{ucharclasses}
\usepackage{xunicode, xltxtra}
\usepackage[inner=1.25cm, outer=0.8cm, top=1.7cm, bottom=0.0cm]{geometry}
\usepackage{pdfpages}
\usepackage{tocloft}
\usepackage{nameref}
\usepackage{verbatim}
\usepackage{relsize}
\usepackage{fontspec}
\usepackage[colorlinks, linkcolor = black]{hyperref}
\usepackage[table]{xcolor}
\usepackage{tabularx}
\usepackage{enumitem}

% 配置字体 (如果系统没有这些字体，可以注释掉相应行)
\usepackage{xeCJK}
% 如果没有 Source Han Serif SC，可以改为 SimSun 或其他中文字体
% \setCJKmainfont{Source Han Serif SC}[Scale=0.8]
% \setCJKmonofont{SimHei}[Scale=0.8]
% \setCJKsansfont{KaiTi}[Scale=0.8]

% 如果没有 Linux Libertine O，可以注释掉这行使用默认字体
% \setmainfont{Linux Libertine O}[Scale=0.925]
% 如果没有 Consolas，可以注释掉这行使用默认等宽字体
% \setmonofont{Consolas}[Scale=0.775]

\XeTeXlinebreaklocale "zh"
\XeTeXlinebreakskip = 0pt plus 1pt

\setlength{\parindent}{0em}\setlength{\parskip}{1pt}
\setlength\itemsep{1pt}

\makeatletter
\renewcommand{\paragraph}{%
  \@startsection{paragraph}{4}%
  {\z@}{1pt \@plus 1pt \@minus 1pt}{-1em}%
  {\normalfont\normalsize\bfseries}%
}
\makeatother

% 配置页眉页脚
\pagestyle{fancy}
\setlength{\headsep}{0.1cm}
\chead{ACM 竞赛算法模板}
\rhead{Page \thepage}
\lhead{算法竞赛模板集}

% 配置双栏间距
\setlength{\columnsep}{13pt}

% 移除目录中的章节编号
% \setcounter{secnumdepth}{0}

% 配置目录样式
\renewcommand\cftsecfont{\Large}

% 配置章节样式
\titleformat{\section}
{\huge}
{\thesection.}
{4pt}
{}
\titlespacing{\section}{0pt}{0pt}{0pt}
\titlespacing{\subsection}{0pt}{0pt}{0pt}
\titlespacing{\subsubsection}{0pt}{0pt}{0pt}

\renewcommand{\theFancyVerbLine}{\sffamily \textcolor[rgb]{0.5,0.5,0.5}{\scriptsize {\arabic{FancyVerbLine}}}}

% 配置 minted 代码高亮
\setminted[cpp]{
	style=xcode,
	mathescape,
	linenos,
	autogobble,
	baselinestretch=0.8,
	tabsize=3,
	fontsize=\normalsize,
	frame=single,
	framesep=1mm,
	framerule=0.3pt,
	numbersep=1mm,
	breaklines=true,
	breaksymbolsepleft=2pt,
	breakbytoken=false,
	showtabs=true,
	tab={\relscale{0.6} $\big\vert \ \ \ $ \relscale{1}},
}

% 自定义命令导入代码
\newcommand{\importcode}[2]{
    \subsection{#1}
    \inputminted{cpp}{#2}
}

\begin{document}
	\begin{titlepage}
		\centering
		\vspace*{2cm}

		{\Huge\bfseries ACM 竞赛算法模板集}

		\vspace{1.5cm}

		{\Large 数据结构 · 图论 · 数学 · 字符串 · 杂项}

		\vspace{2cm}

		\begin{center}
		\begin{tabular}{c}
		\textbf{包含内容：} \\[0.5cm]
		数据结构模板 \\
		图论算法模板 \\
		数学算法模板 \\
		字符串算法模板 \\
		实用工具模板 \\
		\end{tabular}
		\end{center}

		\vfill

		{\large \today}
	\end{titlepage}

	\begin{multicols}{2}
		\setcounter{tocdepth}{3}
		\begingroup
		\let\cleardoublepage\relax
		\let\clearpage\relax
		\begin{small}
		\begin{spacing}{0.70}
		\tableofcontents
		\end{spacing}
		\end{small}
		\newpage
		\begin{spacing}{0.6}

\section{数据结构 (Data Structures)}

\importcode{线段树 (Segment Tree)}{DS/SegmentTree.hpp}

\importcode{懒惰线段树 (Lazy Segment Tree)}{DS/LazySegmentTree.hpp}

\importcode{树状数组 (Binary Indexed Tree)}{DS/BinaryIndexedTree.hpp}

\importcode{并查集 (Disjoint Set Union)}{DS/DisjointSetUnion.hpp}

\importcode{可撤销并查集 (Erasable Disjoint Set Union)}{DS/ErasableDisjointSetUnion.hpp}

\importcode{ST表 (Sparse Table)}{DS/SparseTable.hpp}

\importcode{字典树 (Binary Trie)}{DS/BinaryTrie.hpp}

\importcode{离散化 (Discretizer)}{DS/Discretizer.hpp}

\section{图论 (Graph Theory)}

\importcode{Dijkstra 最短路}{Graph/Dijkstra.hpp}

\importcode{Floyd-Warshall 全源最短路}{Graph/FloydWarshall.hpp}

\importcode{Floyd 算法}{Graph/Floyd.hpp}

\importcode{拓扑排序 (Topological Sort)}{Graph/TopologicalSort.hpp}

\importcode{匈牙利算法 (Hungarian Algorithm)}{Graph/Hungarian.hpp}

\section{数学 (Mathematics)}

\importcode{快速幂 (Fast Power)}{Math/FastPower.hpp}

\importcode{模运算 (Modular Arithmetic)}{Math/ModIntegral.hpp}

\importcode{动态模数 (Dynamic Modular)}{Math/DyModIntegral.hpp}

\importcode{组合数学 (Combinatorics)}{Math/Combination.hpp}

\importcode{矩阵 (Matrix)}{Math/Matrix.hpp}

\importcode{Miller-Rabin 素性测试}{Math/MillerRabin.hpp}

\importcode{Pollard-Rho 因数分解}{Math/PollardRho.hpp}

\importcode{线性筛 (Sieve)}{Math/Sieve.hpp}

\importcode{辛普森积分 (Simpson Integration)}{Math/Simpson.hpp}

\importcode{异或线性基 (XOR Basis)}{Math/XorBase.hpp}

\importcode{Barrett 约简}{Math/Barrett.hpp}

\importcode{前缀异或 (Prefix XOR)}{Math/prexor.hpp}

\section{字符串 (String Algorithms)}

\importcode{Manacher 算法}{String/Manacher.hpp}

\importcode{字符串哈希 (String Hash)}{String/StringHash.hpp}

\section{杂项工具 (Miscellaneous)}

\importcode{大整数 (Big Integer)}{Misc/BigInteger.hpp}

\importcode{动态位集 (Dynamic Bitset)}{Misc/DynamicBitset.hpp}

\importcode{快速输入输出 (Fast I/O)}{Misc/FastIO.hpp}

\importcode{分数 (Fraction)}{Misc/Fraction.hpp}

\importcode{计算几何 (Geometry)}{Misc/Geometry.hpp}

\importcode{计时器 (Timer)}{Misc/Timer.hpp}

\importcode{搜索算法 (Search Algorithms)}{Misc/search.hpp}

\importcode{对拍工具}{Misc/对拍.hpp}

\section{基础工具}

\importcode{类型定义 (Type Definitions)}{types.hpp}

\importcode{调试工具 (Debug Utilities)}{debug.hpp}

\importcode{位运算工具 (Bit Utilities)}{bit.hpp}

\importcode{实用工具 (Utility Tools)}{tool.hpp}

		\end{spacing}
		\endgroup
	\end{multicols}
\end{document}
