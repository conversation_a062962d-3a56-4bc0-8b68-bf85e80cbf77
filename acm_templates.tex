\documentclass[a4paper,10pt,twocolumn]{article}
\usepackage[UTF8]{ctex}
\usepackage[margin=1.5cm]{geometry}
\usepackage{fancyhdr}
\usepackage{listings}
\usepackage{xcolor}
\usepackage{tocloft}
\usepackage{titlesec}
\usepackage{graphicx}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{hyperref}
\usepackage{multicol}

% 设置页眉页脚
\pagestyle{fancy}
\fancyhf{}
\fancyhead[L]{ACM 竞赛模板}
\fancyhead[R]{\thepage}
\renewcommand{\headrulewidth}{0.4pt}

% 设置代码高亮
\definecolor{codegreen}{rgb}{0,0.6,0}
\definecolor{codegray}{rgb}{0.5,0.5,0.5}
\definecolor{codepurple}{rgb}{0.58,0,0.82}
\definecolor{backcolour}{rgb}{0.95,0.95,0.92}

\lstdefinestyle{mystyle}{
    backgroundcolor=\color{backcolour},   
    commentstyle=\color{codegreen},
    keywordstyle=\color{magenta},
    numberstyle=\tiny\color{codegray},
    stringstyle=\color{codepurple},
    basicstyle=\ttfamily\footnotesize,
    breakatwhitespace=false,         
    breaklines=true,                 
    captionpos=b,                    
    keepspaces=true,                 
    numbers=left,                    
    numbersep=5pt,                  
    showspaces=false,                
    showstringspaces=false,
    showtabs=false,                  
    tabsize=2,
    frame=single,
    rulecolor=\color{black}
}

\lstset{style=mystyle}

% 设置标题格式
\titleformat{\section}{\Large\bfseries}{\thesection}{1em}{}
\titleformat{\subsection}{\large\bfseries}{\thesubsection}{1em}{}

% 自定义命令导入代码（跳过pragma once和头文件）
\newcommand{\importcode}[2]{
    \subsection{#1}
    \lstinputlisting[language=C++, firstline=6]{#2}
}

% 自定义命令导入简单代码（跳过pragma once和少量头文件）
\newcommand{\importcodelight}[2]{
    \subsection{#1}
    \lstinputlisting[language=C++, firstline=3]{#2}
}

% 自定义命令导入无头文件代码
\newcommand{\importcodenoheader}[2]{
    \subsection{#1}
    \lstinputlisting[language=C++]{#2}
}

\begin{document}

% 封面
\begin{titlepage}
    \centering
    \vspace*{2cm}
    
    {\Huge\bfseries ACM 竞赛算法模板集}
    
    \vspace{1.5cm}
    
    {\Large 数据结构 · 图论 · 数学 · 字符串 · 杂项}
    
    \vspace{2cm}
    
    \begin{center}
    \begin{tabular}{c}
    \textbf{包含内容：} \\[0.5cm]
    数据结构模板 \\
    图论算法模板 \\
    数学算法模板 \\
    字符串算法模板 \\
    实用工具模板 \\
    \end{tabular}
    \end{center}
    
    \vfill
    
    {\large \today}
    
\end{titlepage}

% 目录
\onecolumn
\tableofcontents
\newpage

% 开始双栏布局
\twocolumn

\section{数据结构 (Data Structures)}

\importcode{线段树 (Segment Tree)}{DS/SegmentTree.hpp}

\importcode{懒惰线段树 (Lazy Segment Tree)}{DS/LazySegmentTree.hpp}

\importcode{树状数组 (Binary Indexed Tree)}{DS/BinaryIndexedTree.hpp}

\importcode{并查集 (Disjoint Set Union)}{DS/DisjointSetUnion.hpp}

\importcode{可撤销并查集 (Erasable Disjoint Set Union)}{DS/ErasableDisjointSetUnion.hpp}

\importcode{ST表 (Sparse Table)}{DS/SparseTable.hpp}

\importcode{字典树 (Binary Trie)}{DS/BinaryTrie.hpp}

\importcode{离散化 (Discretizer)}{DS/Discretizer.hpp}

\section{图论 (Graph Theory)}

\importcode{Dijkstra 最短路}{Graph/Dijkstra.hpp}

\importcode{Floyd-Warshall 全源最短路}{Graph/FloydWarshall.hpp}

\importcode{Floyd 算法}{Graph/Floyd.hpp}

\importcode{拓扑排序 (Topological Sort)}{Graph/TopologicalSort.hpp}

\importcode{匈牙利算法 (Hungarian Algorithm)}{Graph/Hungarian.hpp}

\section{数学 (Mathematics)}

\importcodelight{快速幂 (Fast Power)}{Math/FastPower.hpp}

\importcode{模运算 (Modular Arithmetic)}{Math/ModIntegral.hpp}

\importcode{动态模数 (Dynamic Modular)}{Math/DyModIntegral.hpp}

\importcode{组合数学 (Combinatorics)}{Math/Combination.hpp}

\importcode{矩阵 (Matrix)}{Math/Matrix.hpp}

\importcode{Miller-Rabin 素性测试}{Math/MillerRabin.hpp}

\importcode{Pollard-Rho 因数分解}{Math/PollardRho.hpp}

\importcode{线性筛 (Sieve)}{Math/Sieve.hpp}

\importcode{辛普森积分 (Simpson Integration)}{Math/Simpson.hpp}

\importcode{异或线性基 (XOR Basis)}{Math/XorBase.hpp}

\importcode{Barrett 约简}{Math/Barrett.hpp}

\importcode{前缀异或 (Prefix XOR)}{Math/prexor.hpp}

\section{字符串 (String Algorithms)}

\importcode{Manacher 算法}{String/Manacher.hpp}

\importcode{字符串哈希 (String Hash)}{String/StringHash.hpp}

\section{杂项工具 (Miscellaneous)}

\importcode{大整数 (Big Integer)}{Misc/BigInteger.hpp}

\importcode{动态位集 (Dynamic Bitset)}{Misc/DynamicBitset.hpp}

\importcode{快速输入输出 (Fast I/O)}{Misc/FastIO.hpp}

\importcode{分数 (Fraction)}{Misc/Fraction.hpp}

\importcode{计算几何 (Geometry)}{Misc/Geometry.hpp}

\importcode{计时器 (Timer)}{Misc/Timer.hpp}

\importcode{搜索算法 (Search Algorithms)}{Misc/search.hpp}

\importcode{对拍工具}{Misc/对拍.hpp}

\section{基础工具}

\importcodenoheader{类型定义 (Type Definitions)}{types.hpp}

\importcodenoheader{调试工具 (Debug Utilities)}{debug.hpp}

\importcodenoheader{位运算工具 (Bit Utilities)}{bit.hpp}

\importcodenoheader{实用工具 (Utility Tools)}{tool.hpp}

\end{document}
