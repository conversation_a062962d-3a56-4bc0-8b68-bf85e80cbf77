% ACM 竞赛算法模板集 - 测试版本
% 编译命令: xelatex -shell-escape acm_templates_test.tex

\documentclass[titlepage, a4paper]{article}
\usepackage{graphicx, amssymb, amsmath, textcomp}
\usepackage[T1]{fontenc}
\usepackage{minted}
\usepackage{setspace, titlesec, fancyhdr, multicol}
\usepackage[inner=1.25cm, outer=0.8cm, top=1.7cm, bottom=0.0cm]{geometry}
\usepackage{tocloft}
\usepackage{fontspec}
\usepackage[colorlinks, linkcolor = black]{hyperref}

% 配置字体
\usepackage{xeCJK}
\setmonofont{src/consola}[Scale=0.775]

\XeTeXlinebreaklocale "zh"
\XeTeXlinebreakskip = 0pt plus 1pt

\setlength{\parindent}{0em}\setlength{\parskip}{1pt}

% 配置页眉页脚
\pagestyle{fancy}
\setlength{\headsep}{0.1cm}
\chead{\leftmark}
\rhead{Page \thepage}
\lhead{ACM 算法模板}

% 配置双栏间距
\setlength{\columnsep}{13pt}

% 配置目录样式
\renewcommand\cftsecfont{\Large\bfseries}
\renewcommand\cftsubsecfont{\normalsize}
\setlength{\cftbeforesecskip}{3pt}
\setlength{\cftbeforesubsecskip}{1pt}

% 配置章节样式
\titleformat{\section}
{\huge\bfseries}
{\thesection.}
{4pt}
{}
\titleformat{\subsection}
{\large\bfseries}
{\thesubsection}
{4pt}
{}
\titlespacing{\section}{0pt}{2pt}{2pt}
\titlespacing{\subsection}{0pt}{1pt}{1pt}

% 配置 minted 代码高亮（简化配置）
\setminted[cpp]{
	style=default,
	linenos,
	fontsize=\small,
	frame=single,
	breaklines=true,
	tabsize=4,
}

% 自定义命令导入代码
\newcommand{\importcode}[2]{
    \subsection{#1}
    \inputminted{cpp}{#2}
}

\begin{document}
	\begin{titlepage}
		\centering
		\vspace*{2cm}
		
		{\Huge\bfseries ACM 竞赛算法模板集}
		
		\vspace{1.5cm}
		
		{\Large 数据结构 · 图论 · 数学 · 字符串 · 杂项}
		
		\vspace{2cm}
		
		\begin{center}
		\begin{tabular}{c}
		\textbf{包含内容：} \\[0.5cm]
		数据结构模板 \\
		图论算法模板 \\
		数学算法模板 \\
		字符串算法模板 \\
		实用工具模板 \\
		\end{tabular}
		\end{center}
		
		\vfill
		
		{\large \today}
	\end{titlepage}
	
	\begin{multicols}{2}
		\setcounter{tocdepth}{3}
		\begingroup
		\let\cleardoublepage\relax
		\let\clearpage\relax
		\begin{small}
		\begin{spacing}{0.70}
		\tableofcontents
		\end{spacing}
		\end{small}
		\newpage
		\begin{spacing}{0.6}

\section{数据结构 (Data Structures)}

\importcode{线段树 (Segment Tree)}{DS/SegmentTree.hpp}

\importcode{树状数组 (Binary Indexed Tree)}{DS/BinaryIndexedTree.hpp}

\section{数学 (Mathematics)}

\importcode{快速幂 (Fast Power)}{Math/FastPower.hpp}

\importcode{模运算 (Modular Arithmetic)}{Math/ModIntegral.hpp}

\section{基础工具}

\importcode{类型定义 (Type Definitions)}{types.hpp}

\importcode{调试工具 (Debug Utilities)}{debug.hpp}

		\end{spacing}
		\endgroup
	\end{multicols}
\end{document}
